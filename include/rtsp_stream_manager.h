#ifndef RTSP_STREAM_MANAGER_H
#define RTSP_STREAM_MANAGER_H

#include "common.h"
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>

#ifdef USE_OPENCV
#include <opencv2/opencv.hpp>
#endif

#ifdef USE_FFMPEG
extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/avutil.h>
#include <libswscale/swscale.h>
}
#endif

// 前向声明
class VideoEncoder;
class StreamClient;

/**
 * @brief RTSP推流配置结构
 */
struct RTSPStreamConfig {
    bool enabled = false;                    // 是否启用推流
    std::string serverAddress = "0.0.0.0";  // 服务器地址
    int port = 8554;                         // RTSP端口
    std::string streamPath = "/live";        // 流路径
    
    // 视频编码参数
    std::string codec = "h264";              // 编码格式
    int bitrate = 2000000;                   // 码率 (bps)
    int fps = 15;                           // 帧率
    int width = 640;                        // 视频宽度
    int height = 480;                       // 视频高度
    std::string quality = "medium";          // 质量等级: low, medium, high
    
    // 推流参数
    int maxClients = 10;                    // 最大客户端连接数
    int bufferSize = 30;                    // 缓冲区大小
    int timeoutMs = 5000;                   // 连接超时时间
    
    // 性能参数
    bool enableHardwareAccel = false;       // 硬件加速
    int encoderThreads = 2;                 // 编码线程数
    std::string preset = "fast";            // 编码预设
};

/**
 * @brief 推流状态枚举
 */
enum class StreamStatus {
    STOPPED,        // 已停止
    STARTING,       // 启动中
    RUNNING,        // 运行中
    STOPPING,       // 停止中
    ERROR          // 错误状态
};

/**
 * @brief 推流统计信息
 */
struct StreamStats {
    std::atomic<uint64_t> totalFrames{0};      // 总帧数
    std::atomic<uint64_t> encodedFrames{0};    // 已编码帧数
    std::atomic<uint64_t> droppedFrames{0};    // 丢弃帧数
    std::atomic<uint64_t> totalBytes{0};       // 总字节数
    std::atomic<int> currentClients{0};        // 当前客户端数
    std::atomic<double> currentFPS{0.0};       // 当前帧率
    std::atomic<double> avgBitrate{0.0};       // 平均码率
    
    std::chrono::steady_clock::time_point startTime;  // 开始时间
    mutable std::mutex statsMutex;
    
    void reset() {
        totalFrames = 0;
        encodedFrames = 0;
        droppedFrames = 0;
        totalBytes = 0;
        currentClients = 0;
        currentFPS = 0.0;
        avgBitrate = 0.0;
        startTime = std::chrono::steady_clock::now();
    }
};

/**
 * @brief RTSP推流管理器
 * 
 * 负责管理视频流的编码和RTSP推送，支持多客户端连接
 * 设计原则：高内聚低耦合，不影响主要的缺陷检测功能
 */
class RTSPStreamManager {
public:
    RTSPStreamManager();
    ~RTSPStreamManager();
    
    // 禁用拷贝构造和赋值
    RTSPStreamManager(const RTSPStreamManager&) = delete;
    RTSPStreamManager& operator=(const RTSPStreamManager&) = delete;
    
    /**
     * @brief 初始化推流管理器
     * @param config 推流配置
     * @return 是否初始化成功
     */
    bool initialize(const RTSPStreamConfig& config);
    
    /**
     * @brief 启动推流服务
     * @return 是否启动成功
     */
    bool start();
    
    /**
     * @brief 停止推流服务
     */
    void stop();
    
    /**
     * @brief 推送视频帧
     * @param frame OpenCV图像帧
     * @param cameraId 摄像头ID
     * @return 是否推送成功
     */
    bool pushFrame(const cv::Mat& frame, int cameraId = 0);
    
    /**
     * @brief 获取推流状态
     * @return 当前推流状态
     */
    StreamStatus getStatus() const { return status_; }
    
    /**
     * @brief 获取推流统计信息
     * @return 统计信息结构
     */
    const StreamStats& getStats() const { return stats_; }
    
    /**
     * @brief 更新推流配置
     * @param config 新的配置
     * @return 是否更新成功
     */
    bool updateConfig(const RTSPStreamConfig& config);
    
    /**
     * @brief 获取当前配置
     * @return 当前配置
     */
    const RTSPStreamConfig& getConfig() const { return config_; }
    
    /**
     * @brief 获取RTSP URL
     * @return RTSP访问URL
     */
    std::string getRTSPUrl() const;
    
    /**
     * @brief 检查是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return initialized_; }
    
    /**
     * @brief 检查是否正在运行
     * @return 是否正在运行
     */
    bool isRunning() const { return status_ == StreamStatus::RUNNING; }

private:
    // 核心组件
    std::unique_ptr<VideoEncoder> encoder_;     // 视频编码器
    std::unique_ptr<StreamClient> client_;      // 推流客户端
    
    // 配置和状态
    RTSPStreamConfig config_;                   // 推流配置
    std::atomic<StreamStatus> status_;          // 推流状态
    std::atomic<bool> initialized_;             // 初始化标志
    std::atomic<bool> shouldStop_;              // 停止标志
    
    // 统计信息
    mutable StreamStats stats_;                 // 推流统计
    
    // 线程管理
    std::unique_ptr<std::thread> streamThread_; // 推流线程
    std::mutex frameMutex_;                     // 帧数据互斥锁
    std::condition_variable frameCondition_;    // 帧数据条件变量
    std::queue<cv::Mat> frameQueue_;           // 帧数据队列
    
    // 性能监控
    std::chrono::steady_clock::time_point lastStatsUpdate_;
    std::chrono::steady_clock::time_point lastFrameTime_;
    
    /**
     * @brief 推流工作线程
     */
    void streamWorker();
    
    /**
     * @brief 处理单个视频帧
     * @param frame 视频帧
     * @return 是否处理成功
     */
    bool processFrame(const cv::Mat& frame);
    
    /**
     * @brief 更新统计信息
     */
    void updateStats();

    /**
     * @brief 执行质量控制
     */
    void performQualityControl();
    
    /**
     * @brief 清理资源
     */
    void cleanup();
    
    /**
     * @brief 记录错误信息
     * @param message 错误消息
     */
    void logError(const std::string& message) const;
    
    /**
     * @brief 记录信息
     * @param message 信息消息
     */
    void logInfo(const std::string& message) const;
};

#endif // RTSP_STREAM_MANAGER_H
