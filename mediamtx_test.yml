# MediaMTX测试配置文件

# 日志级别
logLevel: info
logDestinations: [stdout]

# API配置
api: true
apiAddress: 127.0.0.1:9997

# 指标配置
metrics: true
metricsAddress: 127.0.0.1:9998

# RTSP服务器配置
rtspAddress: :8554

# RTMP服务器配置
rtmpAddress: :1935

# HLS配置
hlsAddress: :8888

# WebRTC配置
webrtcAddress: :8889

# 路径配置
paths:
  # 测试路径
  live:
    # 允许发布
    publishUser: ""
    publishPass: ""
    publishIPs: []
    
    # 允许读取
    readUser: ""
    readPass: ""
    readIPs: []
    
    # 运行时参数
    runOnInit: ""
    runOnInitRestart: no
    runOnDemand: ""
    runOnDemandRestart: no
    runOnDemandStartTimeout: 10s
    runOnDemandCloseAfter: 10s
    runOnUnDemand: ""
    
    # 源配置
    source: publisher
    sourceFingerprint: ""
    sourceOnDemand: no
    sourceOnDemandStartTimeout: 10s
    sourceOnDemandCloseAfter: 10s
    sourceRedirect: ""
    
    # 发布者配置
    disablePublisherOverride: no
    fallback: ""
    
    # SRT配置
    srtPublishPassphrase: ""
    srtReadPassphrase: ""
    
  # 所有其他路径
  "~^.*":
    publishUser: ""
    publishPass: ""
    readUser: ""
    readPass: ""
