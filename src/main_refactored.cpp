#include "../include/common.h"
#ifdef USE_OPENCV
#include "../include/camera_manager.h"
#include "../include/damage_detection_engine.h"
#include "../include/rtsp_stream_manager.h"
#endif
#include "../include/test/test_manager.h"
#include <iostream>
#include <signal.h>
#include <getopt.h>

// 全局变量用于信号处理
std::atomic<bool> g_running(true);
#ifdef USE_OPENCV
std::unique_ptr<CameraManager> g_cameraManager;
std::unique_ptr<DamageDetectionEngine> g_detectionEngine;
std::unique_ptr<RTSPStreamManager> g_streamManager;
#endif

// 信号处理函数
void signalHandler(int signal) {
    Utils::logInfo("接收到信号 " + std::to_string(signal) + "，准备退出...");
    g_running = false;
#ifdef USE_OPENCV
    if (g_streamManager) {
        g_streamManager->stop();
    }
    if (g_cameraManager) {
        g_cameraManager->stopCapture();
    }
    if (g_detectionEngine) {
        g_detectionEngine->stop();
    }
#endif
}

#ifdef USE_OPENCV
// 显示系统状态
void displaySystemStatus(const CameraManager& manager) {
    std::cout << "\n=== 系统状态 ===" << std::endl;
    
    auto cameraInfos = manager.getAllCameraInfo();
    for (const auto& info : cameraInfos) {
        std::cout << "摄像头 " << info.id << ": ";
        
        switch (info.status) {
            case CameraStatus::DISCONNECTED:
                std::cout << "未连接";
                break;
            case CameraStatus::CONNECTED:
                std::cout << "已连接";
                break;
            case CameraStatus::CAPTURING:
                std::cout << "采集中 (" << std::fixed << std::setprecision(1) 
                         << manager.getActualFPS(info.id) << " fps)";
                break;
            case CameraStatus::ERROR:
                std::cout << "错误: " << info.errorMsg;
                break;
        }
        
        if (info.status != CameraStatus::DISCONNECTED && info.status != CameraStatus::ERROR) {
            std::cout << " [" << info.width << "x" << info.height << "]";
        }
        
        std::cout << std::endl;
    }

    // 显示推流状态
    if (g_streamManager) {
        std::cout << "推流状态: ";
        switch (g_streamManager->getStatus()) {
            case StreamStatus::STOPPED:
                std::cout << "已停止";
                break;
            case StreamStatus::STARTING:
                std::cout << "启动中";
                break;
            case StreamStatus::RUNNING:
                std::cout << "运行中";
                break;
            case StreamStatus::STOPPING:
                std::cout << "停止中";
                break;
            case StreamStatus::ERROR:
                std::cout << "错误";
                break;
        }

        if (g_streamManager->isRunning()) {
            const auto& stats = g_streamManager->getStats();
            std::cout << " (" << std::fixed << std::setprecision(1)
                     << stats.currentFPS.load() << " fps, "
                     << (stats.avgBitrate.load() / 1000) << " kbps)";
            std::cout << std::endl;
            std::cout << "推流地址: " << g_streamManager->getRTSPUrl();
        }
        std::cout << std::endl;
    }

    std::cout << std::endl;
}

// 核心缺损检测循环
void runDamageDetectionLoop() {
    Utils::logInfo("开始拉吊索缺损检测...");
    
    cv::Mat frame;
    std::vector<cv::Mat> frames;
    int processedFrames = 0;
    auto lastStatusTime = std::chrono::steady_clock::now();
    
    while (g_running) {
        // 获取所有摄像头的帧
        if (g_cameraManager->getAllFrames(frames)) {
            // 对每个摄像头的图像进行缺损检测
            for (size_t i = 0; i < frames.size(); ++i) {
                if (!frames[i].empty()) {
                    // 推送视频帧到推流管理器（如果启用）
                    if (g_streamManager && g_streamManager->isRunning()) {
                        // 只推送第一个摄像头的视频流，避免混合多路视频
                        if (i == 0) {
                            g_streamManager->pushFrame(frames[i], i);
                        }
                    }

                    // 执行缺损检测
                    auto results = g_detectionEngine->detectDamage(frames[i], i);

                    // 处理检测结果
                    if (!results.empty()) {
                        Utils::logInfo("摄像头 " + std::to_string(i) + " 检测到 " +
                                     std::to_string(results.size()) + " 个缺损");

                        // 保存检测结果
                        g_detectionEngine->saveResults(results, i);
                    }
                }
            }
            
            processedFrames++;
            
            // 定期显示系统状态
            auto currentTime = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::seconds>(
                currentTime - lastStatusTime).count() >= 10) {
                
                displaySystemStatus(*g_cameraManager);
                Utils::logInfo("已处理帧数: " + std::to_string(processedFrames));
                lastStatusTime = currentTime;
            }
        } else {
            Utils::logWarning("获取摄像头帧失败");
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 控制处理频率
        std::this_thread::sleep_for(std::chrono::milliseconds(1000 / Config::TARGET_FPS));
    }
    
    Utils::logInfo("缺损检测循环结束，总共处理 " + std::to_string(processedFrames) + " 帧");
}

// 初始化系统
bool initializeSystem() {
    Utils::logInfo("开始初始化拉吊索缺损识别系统...");
    
    // 初始化配置
    if (!Config::initializeConfig()) {
        Utils::logError("配置文件加载失败，使用默认配置");
    } else {
        Utils::logInfo("配置文件加载成功");
        Utils::logInfo("摄像头数量: " + std::to_string(Config::CAMERA_COUNT));
    }
    
    // 创建摄像头管理器
    g_cameraManager = std::make_unique<CameraManager>();
    if (!g_cameraManager->initialize()) {
        Utils::logError("摄像头管理器初始化失败");
        return false;
    }
    
    // 启动摄像头采集
    if (!g_cameraManager->startCapture()) {
        Utils::logError("启动摄像头采集失败");
        return false;
    }
    
    // 创建缺损检测引擎
    g_detectionEngine = std::make_unique<DamageDetectionEngine>();
    if (!g_detectionEngine->initialize()) {
        Utils::logError("缺损检测引擎初始化失败");
        return false;
    }

    // 初始化推流管理器（如果启用）
    ConfigManager& configManager = ConfigManager::getInstance();
    if (configManager.getStreamingEnabled()) {
        Utils::logInfo("初始化视频推流功能...");

        g_streamManager = std::make_unique<RTSPStreamManager>();
        RTSPStreamConfig streamConfig;
        streamConfig.enabled = true;
        streamConfig.serverAddress = "localhost";
        streamConfig.port = 8554;
        streamConfig.streamPath = "/live";
        streamConfig.codec = configManager.getVideoCodec();
        streamConfig.width = configManager.getVideoWidth();
        streamConfig.height = configManager.getVideoHeight();
        streamConfig.fps = configManager.getVideoFPS();
        streamConfig.bitrate = configManager.getVideoBitrate();
        streamConfig.preset = configManager.getVideoPreset();
        streamConfig.bufferSize = configManager.getMaxQueueSize();
        streamConfig.maxClients = 10;
        streamConfig.timeoutMs = configManager.getConnectTimeoutMs();

        if (!g_streamManager->initialize(streamConfig)) {
            Utils::logError("推流管理器初始化失败");
            g_streamManager.reset();
        } else {
            Utils::logInfo("推流管理器初始化成功");
            Utils::logInfo("推流地址: " + g_streamManager->getRTSPUrl());

            // 启动推流服务
            if (!g_streamManager->start()) {
                Utils::logError("推流服务启动失败");
                g_streamManager.reset();
            } else {
                Utils::logInfo("推流服务启动成功");
            }
        }
    } else {
        Utils::logInfo("视频推流功能已禁用");
    }

    Utils::logInfo("系统初始化完成");
    return true;
}

// 运行生产模式
void runProductionMode() {
    Utils::logInfo("=== 拉吊索缺损识别系统 - 生产模式 ===");
    Utils::logInfo("版本: 1.0.0");
    
    if (!initializeSystem()) {
        Utils::logError("系统初始化失败");
        return;
    }
    
    // 显示初始状态
    displaySystemStatus(*g_cameraManager);
    
    // 运行核心检测循环
    runDamageDetectionLoop();
}

// 运行测试模式
void runTestMode(const std::string& testType) {
    Utils::logInfo("=== 拉吊索缺损识别系统 - 测试模式 ===");
    
    TestManager testManager;
    
    if (testType == "all") {
        testManager.runAllTests();
    } else if (testType == "basic") {
        testManager.runBasicTests();
    } else if (testType == "camera") {
        testManager.runCameraTests();
    } else if (testType == "system") {
        testManager.runSystemTests();
    } else {
        Utils::logError("未知的测试类型: " + testType);
        Utils::logInfo("支持的测试类型: all, basic, camera, system");
    }
}

// 显示帮助信息
void showHelp() {
    std::cout << "拉吊索缺损识别系统 v1.0.0\n\n";
    std::cout << "用法:\n";
    std::cout << "  FaultDetect                    运行生产模式（默认）\n";
    std::cout << "  FaultDetect --test [类型]      运行测试模式\n";
    std::cout << "  FaultDetect --help             显示帮助信息\n\n";
    std::cout << "测试类型:\n";
    std::cout << "  all                           运行所有测试\n";
    std::cout << "  basic                         运行基础功能测试\n";
    std::cout << "  camera                        运行摄像头功能测试\n";
    std::cout << "  system                        运行系统集成测试\n\n";
    std::cout << "示例:\n";
    std::cout << "  FaultDetect                   # 运行生产模式\n";
    std::cout << "  FaultDetect --test all        # 运行所有测试\n";
    std::cout << "  FaultDetect --test camera     # 只运行摄像头测试\n";
}
#endif // USE_OPENCV

int main(int argc, char* argv[]) {
#ifdef USE_OPENCV
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    // 解析命令行参数
    bool testMode = false;
    std::string testType = "all";
    
    static struct option long_options[] = {
        {"test", optional_argument, 0, 't'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };
    
    int option_index = 0;
    int c;
    
    while ((c = getopt_long(argc, argv, "t::h", long_options, &option_index)) != -1) {
        switch (c) {
            case 't':
                testMode = true;
                if (optarg) {
                    testType = optarg;
                }
                break;
            case 'h':
                showHelp();
                return 0;
            case '?':
                showHelp();
                return 1;
        }
    }
    
    try {
        if (testMode) {
            runTestMode(testType);
        } else {
            runProductionMode();
        }
    } catch (const std::exception& e) {
        Utils::logError("系统运行异常: " + std::string(e.what()));
        return -1;
    }
    
#else
    Utils::logWarning("OpenCV支持: 未启用");
    Utils::logInfo("请安装OpenCV并重新编译以启用完整功能");
    
    // 运行基础测试
    TestManager testManager;
    testManager.runBasicTests();
#endif
    
    Utils::logInfo("系统正常退出");
    return 0;
}
