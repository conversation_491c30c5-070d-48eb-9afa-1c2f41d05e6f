#include "../../include/rtsp_stream_manager.h"
#include "../../include/video_encoder.h"
#include "../../include/stream_client.h"
#include "../../include/common.h"
#include <chrono>
#include <thread>

RTSPStreamManager::RTSPStreamManager() 
    : status_(StreamStatus::STOPPED)
    , initialized_(false)
    , shouldStop_(false) {
    stats_.reset();
}

RTSPStreamManager::~RTSPStreamManager() {
    stop();
}

bool RTSPStreamManager::initialize(const RTSPStreamConfig& config) {
    if (initialized_) {
        logError("推流管理器已经初始化");
        return false;
    }
    
    config_ = config;
    
    // 创建视频编码器
    encoder_ = std::make_unique<VideoEncoder>();
    VideoEncoderConfig encoderConfig;
    encoderConfig.codec = config_.codec;
    encoderConfig.width = config_.width;
    encoderConfig.height = config_.height;
    encoderConfig.fps = config_.fps;
    encoderConfig.bitrate = config_.bitrate;
    encoderConfig.preset = config_.preset;
    encoderConfig.profile = "baseline"; // 实时推流使用baseline
    encoderConfig.threads = config_.encoderThreads;
    encoderConfig.enableHardwareAccel = config_.enableHardwareAccel;
    
    if (!encoder_->initialize(encoderConfig)) {
        logError("视频编码器初始化失败");
        return false;
    }
    
    // 创建推流客户端
    client_ = std::make_unique<StreamClient>();
    StreamClientConfig clientConfig;
    clientConfig.enabled = config_.enabled;
    clientConfig.pushUrl = "rtmp://localhost:1935" + config_.streamPath;
    clientConfig.viewUrl = "rtsp://localhost:8554" + config_.streamPath;
    clientConfig.codec = config_.codec;
    clientConfig.width = config_.width;
    clientConfig.height = config_.height;
    clientConfig.fps = config_.fps;
    clientConfig.bitrate = config_.bitrate;
    clientConfig.preset = config_.preset;
    clientConfig.maxRetries = 3;
    clientConfig.reconnectIntervalMs = 5000;
    clientConfig.bufferSize = config_.bufferSize;
    clientConfig.maxQueueSize = config_.bufferSize;
    
    if (!client_->initialize(clientConfig)) {
        logError("推流客户端初始化失败");
        return false;
    }
    
    initialized_ = true;
    status_ = StreamStatus::STOPPED;
    stats_.reset();
    
    logInfo("推流管理器初始化成功: " + getRTSPUrl());
    return true;
}

bool RTSPStreamManager::start() {
    if (!initialized_) {
        logError("推流管理器未初始化");
        return false;
    }
    
    if (status_ == StreamStatus::RUNNING) {
        logInfo("推流管理器已在运行");
        return true;
    }
    
    status_ = StreamStatus::STARTING;
    
    // 连接推流客户端
    if (!client_->connect()) {
        logError("推流客户端连接失败");
        status_ = StreamStatus::ERROR;
        return false;
    }
    
    // 启动推流工作线程
    shouldStop_ = false;
    streamThread_ = std::make_unique<std::thread>(&RTSPStreamManager::streamWorker, this);
    
    status_ = StreamStatus::RUNNING;
    stats_.reset();
    stats_.startTime = std::chrono::steady_clock::now();
    
    logInfo("推流管理器启动成功");
    return true;
}

void RTSPStreamManager::stop() {
    if (status_ == StreamStatus::STOPPED) {
        return;
    }
    
    status_ = StreamStatus::STOPPING;
    shouldStop_ = true;
    
    // 通知工作线程停止
    frameCondition_.notify_all();
    
    // 等待工作线程结束
    if (streamThread_ && streamThread_->joinable()) {
        streamThread_->join();
        streamThread_.reset();
    }
    
    // 断开推流客户端
    if (client_) {
        client_->disconnect();
    }
    
    // 清理帧队列
    {
        std::lock_guard<std::mutex> lock(frameMutex_);
        while (!frameQueue_.empty()) {
            frameQueue_.pop();
        }
    }
    
    status_ = StreamStatus::STOPPED;
    logInfo("推流管理器已停止");
}

bool RTSPStreamManager::pushFrame(const cv::Mat& frame, int cameraId) {
    if (!isRunning() || frame.empty()) {
        return false;
    }

    // 质量控制：检查帧率
    auto now = std::chrono::steady_clock::now();
    auto timeSinceLastFrame = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastFrameTime_).count();
    int targetInterval = 1000 / config_.fps;

    // 如果帧率过高，跳过此帧
    if (timeSinceLastFrame < targetInterval * 0.8) {
        return true; // 跳过但返回成功
    }

    // 质量控制：检查队列大小，实现自适应丢帧
    {
        std::lock_guard<std::mutex> lock(frameMutex_);
        double queueRatio = static_cast<double>(frameQueue_.size()) / config_.bufferSize;

        // 如果队列使用率超过阈值，开始丢帧
        if (queueRatio > 0.8) {
            // 丢弃一些旧帧以减少延迟
            int framesToDrop = frameQueue_.size() / 3;
            for (int i = 0; i < framesToDrop && !frameQueue_.empty(); ++i) {
                frameQueue_.pop();
                stats_.droppedFrames++;
            }
        }

        // 检查帧尺寸并调整
        cv::Mat processedFrame;
        if (frame.cols != config_.width || frame.rows != config_.height) {
            cv::resize(frame, processedFrame, cv::Size(config_.width, config_.height));
        } else {
            processedFrame = frame.clone();
        }

        // 添加到队列
        if (frameQueue_.size() < config_.bufferSize) {
            frameQueue_.push(processedFrame);
        } else {
            // 队列满，丢弃最旧的帧
            frameQueue_.pop();
            frameQueue_.push(processedFrame);
            stats_.droppedFrames++;
        }
    }

    frameCondition_.notify_one();
    stats_.totalFrames++;
    lastFrameTime_ = now;

    return true;
}

bool RTSPStreamManager::updateConfig(const RTSPStreamConfig& config) {
    if (isRunning()) {
        logError("无法在运行状态下更新配置");
        return false;
    }
    
    config_ = config;
    
    // 重新初始化组件
    cleanup();
    initialized_ = false;
    
    return initialize(config);
}

std::string RTSPStreamManager::getRTSPUrl() const {
    return "rtsp://" + config_.serverAddress + ":" + std::to_string(config_.port) + config_.streamPath;
}

void RTSPStreamManager::streamWorker() {
    logInfo("推流工作线程启动");
    
    while (!shouldStop_) {
        cv::Mat frame;
        
        // 获取帧数据
        {
            std::unique_lock<std::mutex> lock(frameMutex_);
            frameCondition_.wait_for(lock, std::chrono::milliseconds(100),
                                   [this] { return !frameQueue_.empty() || shouldStop_; });
            
            if (shouldStop_) {
                break;
            }
            
            if (frameQueue_.empty()) {
                continue;
            }
            
            frame = frameQueue_.front();
            frameQueue_.pop();
        }
        
        // 处理帧
        if (!processFrame(frame)) {
            logError("处理帧失败");
            stats_.droppedFrames++;
        }
        
        updateStats();
    }
    
    logInfo("推流工作线程结束");
}

bool RTSPStreamManager::processFrame(const cv::Mat& frame) {
    if (!encoder_ || !client_) {
        return false;
    }
    
    // 编码帧
    EncodedPacket packet;
    if (!encoder_->encode(frame, packet)) {
        return false;
    }
    
    // 如果没有编码输出（需要更多输入帧），返回成功
    if (packet.size == 0) {
        return true;
    }
    
    // 发送编码数据包
    if (!client_->sendPacket(packet)) {
        return false;
    }
    
    stats_.encodedFrames++;
    stats_.totalBytes += packet.size;
    
    return true;
}

void RTSPStreamManager::updateStats() {
    auto now = std::chrono::steady_clock::now();
    auto timeDiff = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastFrameTime_).count();

    if (timeDiff > 0) {
        stats_.currentFPS = 1000.0 / timeDiff;
    }

    lastFrameTime_ = now;

    // 计算平均码率
    auto totalTime = std::chrono::duration_cast<std::chrono::seconds>(now - stats_.startTime).count();
    if (totalTime > 0) {
        stats_.avgBitrate = (stats_.totalBytes * 8.0) / totalTime;
    }

    // 更新客户端数量（对于外部服务器，这个值通常为1或0）
    if (client_ && client_->isConnected()) {
        stats_.currentClients = 1;
    } else {
        stats_.currentClients = 0;
    }

    // 自适应质量控制
    performQualityControl();
}

void RTSPStreamManager::performQualityControl() {
    if (!encoder_ || !client_) {
        return;
    }

    // 检查丢帧率
    uint64_t totalFrames = stats_.totalFrames.load();
    uint64_t droppedFrames = stats_.droppedFrames.load();

    if (totalFrames > 100) { // 有足够的样本数据
        double dropRate = static_cast<double>(droppedFrames) / totalFrames;

        // 如果丢帧率过高，降低码率
        if (dropRate > 0.1) { // 丢帧率超过10%
            int currentBitrate = encoder_->getCurrentBitrate();
            int newBitrate = static_cast<int>(currentBitrate * 0.8); // 降低20%

            // 确保不低于最小码率
            if (newBitrate < 500000) { // 最小500kbps
                newBitrate = 500000;
            }

            if (newBitrate != currentBitrate) {
                encoder_->adjustBitrate(newBitrate);
                logInfo("由于丢帧率过高(" + std::to_string(dropRate * 100) +
                       "%)，码率调整为: " + std::to_string(newBitrate / 1000) + " kbps");
            }
        }
        // 如果丢帧率很低且连接稳定，可以适当提高码率
        else if (dropRate < 0.02 && client_->isConnected()) { // 丢帧率低于2%
            int currentBitrate = encoder_->getCurrentBitrate();
            int maxBitrate = config_.bitrate; // 不超过配置的最大码率

            if (currentBitrate < maxBitrate) {
                int newBitrate = static_cast<int>(currentBitrate * 1.1); // 提高10%
                if (newBitrate > maxBitrate) {
                    newBitrate = maxBitrate;
                }

                if (newBitrate != currentBitrate) {
                    encoder_->adjustBitrate(newBitrate);
                    logInfo("连接稳定，码率调整为: " + std::to_string(newBitrate / 1000) + " kbps");
                }
            }
        }
    }
}

void RTSPStreamManager::cleanup() {
    encoder_.reset();
    client_.reset();
}

void RTSPStreamManager::logError(const std::string& message) const {
    Utils::logError("RTSPStreamManager: " + message);
}

void RTSPStreamManager::logInfo(const std::string& message) const {
    Utils::logInfo("RTSPStreamManager: " + message);
}
